# Version of the Compose file format
# Version 3 is the most current and recommended one
version: "3"

# Top building block that defines
# All containers used for this service
services:

  # https://hub.docker.com/_/mysql
  db:
    # Image name (optionally version)
    image: mysql:5.7
    # Define restart policy
    # https://docs.docker.com/compose/compose-file/#restart
    # restart: always
    # Volumes definition
    # Named volume, allows persisted data but without caring where locally it is stored
    # https://nickjanetakis.com/blog/docker-tip-28-named-volumes-vs-path-based-volumes
    volumes:
      - db_data:/var/lib/mysql
    # Add environment variables
    # https://docs.docker.com/compose/compose-file/#environment
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: wordpress
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress
    # List of networks to join
    networks:
      - wp

  # https://hub.docker.com/_/wordpress
  wordpress:
    depends_on:
      - db
    # Feel free to add a version of WordPress
    # I.e. wordpress:5.2.0
    image: wordpress:latest
    # Define restart policy
    # restart: always
    # Maps your local folder, to path in a container
    # Useful for file edits
    # I like to map only wp-content
    # We should not care about WP core files
    volumes:
      - ./wp:/var/www/html/wp-content
      - ./php/uploads.ini:/usr/local/etc/php/conf.d/uploads.ini
    environment:
      WORDPRESS_DB_HOST: db:3306
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress
    # Ports mapping
    ports:
      - 80:80
      - 443:443
    networks:
      - wp

  # https://hub.docker.com/r/phpmyadmin/phpmyadmin
  phpmyadmin:
    depends_on:
      - db
    # Image name (optionally version)
    # https://docs.docker.com/compose/compose-file/#image
    image: phpmyadmin/phpmyadmin
    # Define restart policy
    # https://docs.docker.com/compose/compose-file/#restart
    # restart: always
    # Ports mapping
    ports:
      - 8080:80
    volumes:
      - ./php/uploads.ini:/usr/local/etc/php/conf.d/uploads.ini
    # Add environment variables
    # https://docs.docker.com/compose/compose-file/#environment
    environment:
      PMA_HOST: db
      MYSQL_ROOT_PASSWORD: password
    networks:
      - wp

networks:
  wp:

volumes:
  db_data: