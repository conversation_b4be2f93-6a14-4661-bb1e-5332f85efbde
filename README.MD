# WordPress development skeleton

It should install (almost) the latest version of WordPress when started for the first time.
If it's not just press update on dashboard...

The `wp` folder mounted in container as `/var/www/html/wp-content`.

MySQL DB data is not explicitly mounted in folder.
It can be mounted of course, but <PERSON><PERSON>'s named volume shit is enough for now.

There's an example for a child theme for the built-in 2020 theme.

Usage
---------
1. Checkout repository with `git clone`
2. Change git remote `git remote set-url` to the correspondent project git url
3. Change `.gitignore` to match requirements (eg. for theme dev: add parent and child to root folder and mount in `docker-compose.yml`)
4. (optional) Change ports if it's colliding  
  Default ports:
  * MySQL: not exposed...
  * WordPress (webserver): `80` and `443`
  * PHPMyAdmin: `8080`
5. start docker (it's fully configured): `docker-compose up`
6. Hurray! You can enjoy your highly efficient (LOL) WordPress development environment. So [install](https://localhost/) WordPress... 


Credentials
---------
###### MySQL
Root password: `password`  
Simple user's username: `wordpress`  
Simple user's password: `wordpress`

###### WordPress
Username: `TODO change this after install completed`  
Password: `TODO change this after install completed`
